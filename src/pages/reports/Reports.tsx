import React, { useState, useEffect } from 'react';
import { reportService, SalesReport, ProductSalesReport, InventoryReport } from '../../services/reportService';
import { useToast } from '../../hooks/useToast';
import Button from '../../components/ui/Button';
import Card from '../../components/ui/Card';

const Reports: React.FC = () => {
  const [salesData, setSalesData] = useState<SalesReport[]>([]);
  const [productSalesData, setProductSalesData] = useState<ProductSalesReport[]>([]);
  const [inventoryData, setInventoryData] = useState<InventoryReport | null>(null);
  const [loading, setLoading] = useState(false);
  const [dateRange, setDateRange] = useState({
    startDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 30 days ago
    endDate: new Date().toISOString().split('T')[0], // Today
  });

  const { showError, showSuccess } = useToast();

  useEffect(() => {
    fetchReports();
  }, []);

  const fetchReports = async () => {
    try {
      setLoading(true);
      const [sales, productSales, inventory] = await Promise.all([
        reportService.getSalesReport(dateRange.startDate, dateRange.endDate),
        reportService.getProductSalesReport(dateRange.startDate, dateRange.endDate),
        reportService.getInventoryReport(),
      ]);

      setSalesData(sales);
      setProductSalesData(productSales);
      setInventoryData(inventory);
    } catch (error) {
      console.error('Failed to fetch reports:', error);
      showError('Failed to load reports');
    } finally {
      setLoading(false);
    }
  };

  const handleDateRangeChange = () => {
    fetchReports();
  };

  const handleExportSales = async () => {
    try {
      const csvData = await reportService.exportSalesReport(dateRange.startDate, dateRange.endDate);
      // Create and download CSV file
      const blob = new Blob([csvData], { type: 'text/csv' });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `sales-report-${dateRange.startDate}-to-${dateRange.endDate}.csv`;
      a.click();
      window.URL.revokeObjectURL(url);
      showSuccess('Sales report exported successfully');
    } catch (error) {
      console.error('Failed to export sales report:', error);
      showError('Failed to export sales report');
    }
  };

  const handleExportInventory = async () => {
    try {
      const csvData = await reportService.exportInventoryReport();
      const blob = new Blob([csvData], { type: 'text/csv' });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `inventory-report-${new Date().toISOString().split('T')[0]}.csv`;
      a.click();
      window.URL.revokeObjectURL(url);
      showSuccess('Inventory report exported successfully');
    } catch (error) {
      console.error('Failed to export inventory report:', error);
      showError('Failed to export inventory report');
    }
  };

  if (loading && !salesData.length) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto"></div>
          <p className="mt-4 text-gray-600 dark:text-gray-400">Loading reports...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-foreground">Reports & Analytics</h1>
          <p className="text-muted-foreground mt-1">
            View sales performance and inventory analytics
          </p>
        </div>
      </div>

      {/* Date Range Filter */}
      <Card title="Report Filters">
        <div className="flex items-center space-x-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Start Date
            </label>
            <input
              type="date"
              value={dateRange.startDate}
              onChange={(e) => setDateRange(prev => ({ ...prev, startDate: e.target.value }))}
              className="border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              End Date
            </label>
            <input
              type="date"
              value={dateRange.endDate}
              onChange={(e) => setDateRange(prev => ({ ...prev, endDate: e.target.value }))}
              className="border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
            />
          </div>
          <div className="pt-6">
            <Button onClick={handleDateRangeChange} disabled={loading}>
              {loading ? 'Loading...' : 'Update Reports'}
            </Button>
          </div>
        </div>
      </Card>

      {/* Sales Overview */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card 
          title={
            <div className="flex items-center justify-between">
              <span>Sales Overview</span>
              <Button variant="outline" size="sm" onClick={handleExportSales}>
                Export CSV
              </Button>
            </div>
          }
        >
          {salesData.length > 0 ? (
            <div className="space-y-4">
              <div className="grid grid-cols-3 gap-4">
                <div className="text-center">
                  <p className="text-2xl font-bold text-primary-600">
                    ${salesData.reduce((sum, day) => sum + (day.total_sales || 0), 0).toFixed(2)}
                  </p>
                  <p className="text-sm text-gray-600 dark:text-gray-400">Total Sales</p>
                </div>
                <div className="text-center">
                  <p className="text-2xl font-bold text-primary-600">
                    {salesData.reduce((sum, day) => sum + (day.total_orders || 0), 0)}
                  </p>
                  <p className="text-sm text-gray-600 dark:text-gray-400">Total Orders</p>
                </div>
                <div className="text-center">
                  <p className="text-2xl font-bold text-primary-600">
                    ${salesData.length > 0
                      ? (salesData.reduce((sum, day) => sum + (day.average_order_value || 0), 0) / salesData.length).toFixed(2)
                      : '0.00'
                    }
                  </p>
                  <p className="text-sm text-gray-600 dark:text-gray-400">Avg Order Value</p>
                </div>
              </div>
              
              <div className="max-h-64 overflow-y-auto">
                <table className="w-full text-sm">
                  <thead>
                    <tr className="border-b border-gray-200 dark:border-gray-700">
                      <th className="text-left py-2">Date</th>
                      <th className="text-right py-2">Sales</th>
                      <th className="text-right py-2">Orders</th>
                    </tr>
                  </thead>
                  <tbody>
                    {salesData.map((day, index) => (
                      <tr key={index} className="border-b border-gray-100 dark:border-gray-800">
                        <td className="py-2">{new Date(day.date).toLocaleDateString()}</td>
                        <td className="text-right py-2">${(day.total_sales || 0).toFixed(2)}</td>
                        <td className="text-right py-2">{day.total_orders || 0}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          ) : (
            <div className="text-center py-8">
              <p className="text-gray-500 dark:text-gray-400">No sales data for selected period</p>
            </div>
          )}
        </Card>

        <Card title="Top Selling Products">
          {productSalesData.length > 0 ? (
            <div className="max-h-80 overflow-y-auto">
              <table className="w-full text-sm">
                <thead>
                  <tr className="border-b border-gray-200 dark:border-gray-700">
                    <th className="text-left py-2">Product</th>
                    <th className="text-right py-2">Qty Sold</th>
                    <th className="text-right py-2">Revenue</th>
                  </tr>
                </thead>
                <tbody>
                  {productSalesData.slice(0, 10).map((product, index) => (
                    <tr key={index} className="border-b border-gray-100 dark:border-gray-800">
                      <td className="py-2">{product.product_name}</td>
                      <td className="text-right py-2">{product.quantity_sold}</td>
                      <td className="text-right py-2">${product.total_revenue.toFixed(2)}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          ) : (
            <div className="text-center py-8">
              <p className="text-gray-500 dark:text-gray-400">No product sales data</p>
            </div>
          )}
        </Card>
      </div>

      {/* Inventory Analytics */}
      <Card 
        title={
          <div className="flex items-center justify-between">
            <span>Inventory Analytics</span>
            <Button variant="outline" size="sm" onClick={handleExportInventory}>
              Export CSV
            </Button>
          </div>
        }
      >
        {inventoryData ? (
          <div className="space-y-6">
            <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
              <div className="text-center">
                <p className="text-2xl font-bold text-primary-600">{inventoryData.total_products}</p>
                <p className="text-sm text-gray-600 dark:text-gray-400">Total Products</p>
              </div>
              <div className="text-center">
                <p className="text-2xl font-bold text-primary-600">${inventoryData.total_value.toFixed(2)}</p>
                <p className="text-sm text-gray-600 dark:text-gray-400">Total Value</p>
              </div>
              <div className="text-center">
                <p className="text-2xl font-bold text-red-600">{inventoryData.low_stock_count}</p>
                <p className="text-sm text-gray-600 dark:text-gray-400">Low Stock Items</p>
              </div>
              <div className="text-center">
                <p className="text-2xl font-bold text-yellow-600">{inventoryData.expiring_soon_count}</p>
                <p className="text-sm text-gray-600 dark:text-gray-400">Expiring Soon</p>
              </div>
            </div>

            {inventoryData.categories.length > 0 && (
              <div>
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Inventory by Category</h3>
                <div className="max-h-64 overflow-y-auto">
                  <table className="w-full text-sm">
                    <thead>
                      <tr className="border-b border-gray-200 dark:border-gray-700">
                        <th className="text-left py-2">Category</th>
                        <th className="text-right py-2">Products</th>
                        <th className="text-right py-2">Total Value</th>
                      </tr>
                    </thead>
                    <tbody>
                      {inventoryData.categories.map((category, index) => (
                        <tr key={index} className="border-b border-gray-100 dark:border-gray-800">
                          <td className="py-2">{category.category}</td>
                          <td className="text-right py-2">{category.product_count}</td>
                          <td className="text-right py-2">${category.total_value.toFixed(2)}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            )}
          </div>
        ) : (
          <div className="text-center py-8">
            <p className="text-gray-500 dark:text-gray-400">No inventory data available</p>
          </div>
        )}
      </Card>
    </div>
  );
};

export default Reports;
